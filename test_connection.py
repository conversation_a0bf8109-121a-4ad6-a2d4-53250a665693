import asyncio
from config import TestConfig
import databases

# Load config and build DB URL
config = TestConfig()
db_url = config.build_database_url()
database = databases.Database(db_url)
                                             
async def test_connection():
    try:
        await database.connect()
        print("✅ Database connection successful!")

        row = await database.fetch_one("SELECT VERSION()")
        print("Database version:", row[0])

    except Exception as e:
        print("❌ Connection failed:", str(e))
    finally:
        await database.disconnect()

if __name__ == "__main__":
    asyncio.run(test_connection())
