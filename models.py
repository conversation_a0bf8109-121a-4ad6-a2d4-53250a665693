from sqlalchemy import create_engine, text
from config import TestConfig
from datetime import datetime, timedelta
import csv

# Setup DB connection
config = TestConfig()
db_url = config.build_database_url()
engine = create_engine(db_url)

# === Transaction types to include in report ===
ALL_TRANSACTION_TYPES = [
    "Bank To OneMoney", "Cash In", "Sell Airtime/Bundle",
    "Send Money", "Transfer For Customer", "Transfer Refund", "Send Money Reversal",
    "OneMoney To Bank", "Cash Out", "ZIPIT Send", "ZIPIT Receive", "ZIPIT Reversal",
    "Payment", "Bill Payment", "Buy Airtime/Bundle", "Sell ZESA", "Third Party Paying",
    "Refund", "Balance Enquiry", "Statement Enquiry", "ZESA", "POS", "Online Payment"
]

# === SQL query for yesterday's transactions ===
def get_transaction_summary():
    yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')

    query = text(f"""
        WITH combined_orders AS (
        SELECT trans_type, fee_amt, order_status, currency, DATE(created_time) AS tx_date
        FROM mfs_xwallet_uat.t_transfer_order_inf
        WHERE DATE(created_time) = '{yesterday}'

        UNION ALL
        SELECT business_type AS trans_type, fee_amt, order_status, currency, DATE(created_time)
        FROM mfs_xwallet_uat.t_enquiry_service_order_inf
        WHERE DATE(created_time) = '{yesterday}'

        UNION ALL
        SELECT trans_type, one_money_fee_amt AS fee_amt, order_status, currency, DATE(created_time)
        FROM mfs_xwallet_uat.t_zipit_order_inf
        WHERE DATE(created_time) = '{yesterday}'

        UNION ALL
        SELECT trans_type, fee_amt, order_status, currency, DATE(created_time)
        FROM mfs_xwallet_uat.t_top_up_order_inf
        WHERE DATE(created_time) = '{yesterday}'

        UNION ALL
        SELECT 'POS' AS trans_type, one_money_fee_amt AS fee_amt, order_status, currency, DATE(created_time)
        FROM mfs_xwallet_uat.t_pay_order_inf
        WHERE DATE(created_time) = '{yesterday}' AND mer_name = 'ZSS'

        UNION ALL
        SELECT 'Online Payment' AS trans_type, one_money_fee_amt AS fee_amt, order_status, currency, DATE(created_time)
        FROM mfs_xwallet_uat.t_pay_order_inf
        WHERE DATE(created_time) = '{yesterday}' AND payment_product = '09' AND rec_cst_name <> 'COH'

        UNION ALL
        SELECT trans_type, one_money_fee_amt AS fee_amt, order_status, currency, DATE(created_time)
        FROM mfs_xwallet_uat.t_pay_order_inf
        WHERE DATE(created_time) = '{yesterday}' AND payment_product = '05'

        UNION ALL
        SELECT trx_trans_type AS trans_type, trx_fee_amt AS fee_amt, trx_order_status AS order_status, trx_currency AS currency, DATE(created_time)
        FROM mfs_xwallet_uat.t_biller_order_inf
        WHERE DATE(created_time) = '{yesterday}'

        UNION ALL
        SELECT trans_type, fee_amt, order_status, currency, DATE(created_time)
        FROM mfs_xwallet_uat.t_customer_top_up_order_inf
        WHERE DATE(created_time) = '{yesterday}'

        UNION ALL
        SELECT trans_type, fee_amount AS fee_amt, order_status, currency, DATE(created_time)
        FROM mfs_xwallet_uat.t_customer_withdrawal_order_inf
        WHERE DATE(created_time) = '{yesterday}'

        UNION ALL
        SELECT trans_type, trans_amt AS fee_amt, order_status, currency, DATE(created_time)
        FROM mfs_xwallet_uat.t_customer_biller_order_inf
        WHERE DATE(created_time) = '{yesterday}'

        UNION ALL
        SELECT trans_type, fee_amt, order_status, currency, DATE(created_time)
        FROM mfs_xwallet_uat.t_withdraw_order_inf
        WHERE DATE(created_time) = '{yesterday}'
    )

    SELECT
        CASE
            WHEN trans_type IN ('26', '27') AND (fee_amt IS NULL OR fee_amt = 0.00) THEN 'ZIPIT Receive'
            WHEN trans_type IN ('26', '27') AND fee_amt > 0.00 THEN 'ZIPIT Send'
            WHEN trans_type = '00' THEN 'Bank To OneMoney'
            WHEN trans_type = '01' THEN 'Cash In'
            WHEN trans_type = '02' THEN 'Sell Airtime/Bundle'
            WHEN trans_type = '10' THEN 'Send Money'
            WHEN trans_type = '11' THEN 'Transfer For Customer'
            WHEN trans_type = '12' THEN 'Transfer Refund'
            WHEN trans_type = '13' THEN 'Send Money Reversal'
            WHEN trans_type IN ('20', '21') THEN 'OneMoney To Bank'
            WHEN trans_type = '22' THEN 'Cash Out'
            WHEN trans_type = '28' THEN 'ZIPIT Reversal'
            WHEN trans_type = '30' THEN 'Payment'
            WHEN trans_type = '31' THEN 'Bill Payment'
            WHEN trans_type = '32' THEN 'Buy Airtime/Bundle'
            WHEN trans_type = '33' THEN 'Sell ZESA'
            WHEN trans_type = '34' THEN 'Third Party Paying'
            WHEN trans_type = '50' THEN 'Refund'
            WHEN trans_type = '65' THEN 'Balance Enquiry'
            WHEN trans_type = '66' THEN 'Statement Enquiry'
            WHEN trans_type = 'ZESA' THEN 'ZESA'
            WHEN trans_type = 'POS' THEN 'POS'
            WHEN trans_type = 'Online Payment' THEN 'Online Payment'
        END AS trade_type_description,
        currency,
        COUNT(*) AS total_transactions,
        SUM(CASE WHEN order_status = 30 THEN 1 ELSE 0 END) AS successful_transactions,
        SUM(CASE WHEN order_status != 30 THEN 1 ELSE 0 END) AS failed_transactions
    FROM combined_orders
    WHERE
    CASE
        WHEN trans_type IN ('26', '27') AND (fee_amt IS NULL OR fee_amt = 0.00) THEN 'ZIPIT Receive'
        WHEN trans_type IN ('26', '27') AND fee_amt > 0.00 THEN 'ZIPIT Send'
        WHEN trans_type = '00' THEN 'Bank To OneMoney'
        WHEN trans_type = '01' THEN 'Cash In'
        WHEN trans_type = '02' THEN 'Sell Airtime/Bundle'
        WHEN trans_type = '10' THEN 'Send Money'
        WHEN trans_type = '11' THEN 'Transfer For Customer'
        WHEN trans_type = '12' THEN 'Transfer Refund'
        WHEN trans_type = '13' THEN 'Send Money Reversal'
        WHEN trans_type IN ('20', '21') THEN 'OneMoney To Bank'
        WHEN trans_type = '22' THEN 'Cash Out'
        WHEN trans_type = '28' THEN 'ZIPIT Reversal'
        WHEN trans_type = '30' THEN 'Payment'
        WHEN trans_type = '31' THEN 'Bill Payment'
        WHEN trans_type = '32' THEN 'Buy Airtime/Bundle'
        WHEN trans_type = '33' THEN 'Sell ZESA'
        WHEN trans_type = '34' THEN 'Third Party Paying'
        WHEN trans_type = '50' THEN 'Refund'
        WHEN trans_type = '65' THEN 'Balance Enquiry'
        WHEN trans_type = '66' THEN 'Statement Enquiry'
        WHEN trans_type = 'ZESA' THEN 'ZESA'
        WHEN trans_type = 'POS' THEN 'POS'
        WHEN trans_type = 'Online Payment' THEN 'Online Payment'
        ELSE NULL
    END IS NOT NULL
    GROUP BY trade_type_description, currency
""")

    with engine.connect() as conn:
        result = conn.execute(query).fetchall()
        return [dict(row._mapping) for row in result]

# === Pad missing transaction types ===
def pad_missing_transaction_types(stats):
    padded = []
    currencies = set(row["currency"] for row in stats)
    for currency in currencies:
        existing_types = {row["trade_type_description"] for row in stats if row["currency"] == currency}
        for tx_type in ALL_TRANSACTION_TYPES:
            if tx_type not in existing_types:
                padded.append({
                    "trade_type_description": tx_type,
                    "currency": currency,
                    "total_transactions": 0,
                    "successful_transactions": 0,
                    "failed_transactions": 0
                })
    return stats + padded
# Export to CSV
def export_to_csv(stats, filename="transaction_report.csv"):
    headers = ["trade_type_description", "currency","total_transactions", "successful_transactions", "failed_transactions"]
    with open(filename, mode="w", newline="") as file:
        writer = csv.DictWriter(file, fieldnames=headers)
        writer.writeheader()
        for row in stats:
            writer.writerow(row)
    return filename

# Master function to generate both files
def generate_reports():
    stats = get_transaction_summary()
    stats = pad_missing_transaction_types(stats)

    usd_stats = [row for row in stats if row["currency"] == "USD"]
    zwg_stats = [row for row in stats if row["currency"] == "ZWG"]

    csv_usd = export_to_csv(usd_stats, filename="transaction_report_usd.csv")
    csv_zwg = export_to_csv(zwg_stats, filename="transaction_report_zwg.csv")
    

    return {
        "USD": {"csv": csv_usd},
        "ZWG": {"csv": csv_zwg}
    }
