from pydantic_settings import BaseSettings, SettingsConfigDict
from functools import lru_cache
import urllib.parse
import os

class TestConfig(BaseSettings):
    DB_HOST: str
    DB_PORT: str
    DB_NAME: str
    DB_USER: str
    DB_PASS: str

    model_config = SettingsConfigDict(
        env_file=".env",
        env_prefix="TEST_",
        extra="ignore"
    )

    def build_database_url(self) -> str:
        password = urllib.parse.quote_plus(self.DB_PASS)
        return f"mysql+pymysql://{self.DB_USER}:{password}@{self.DB_HOST}:{self.DB_PORT}/{self.DB_NAME}"

class ProdConfig(BaseSettings):
    # Define PROD fields here if needed
    model_config = SettingsConfigDict(
        env_prefix="PROD_", 
        env_file=".env",
        extra="ignore"
        )

@lru_cache()
def get_config():
    env_state = os.getenv("ENV_STATE", "TEST")
    print("Loaded ENV_STATE:", env_state)

    configs = {"TEST": TestConfig, "PROD": ProdConfig}
    if env_state not in configs:
        raise ValueError(f"Invalid ENV_STATE: {env_state}")

    config_instance = configs[env_state]()
    print("Loaded config:", config_instance.model_dump())
    return config_instance



