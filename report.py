import smtplib
from email.message import EmailMessage
from models import generate_reports
from dotenv import load_dotenv
import os

load_dotenv()

#Getting email credentials from the .env
SMTP_HOST = os.getenv("SMTP_HOST")
SMTP_PORT = int(os.getenv("SMTP_PORT"))
SENDER_EMAIL = os.getenv("SENDER_EMAIL")
SENDER_PASSWORD = os.getenv("SENDER_PASSWORD")
RECIPIENT_EMAIL = os.getenv("RECIPIENT_EMAIL")

def send_report_email():
    reports = generate_reports()

    msg = EmailMessage()
    msg["Subject"] = "Daily Transaction Reports"
    msg["From"] = SENDER_EMAIL
    msg["To"] = RECIPIENT_EMAIL
    msg.set_content("Good day,\n\nPlease find attached the daily transaction reports for USD and ZWG.\n\nRegards,\nChaps Bot")

    
    for currency, paths in reports.items():
        csv_path = paths.get("csv")
        if csv_path:
            with open(csv_path, "rb") as f:
                file_data = f.read()
                msg.add_attachment(
                    file_data,
                    maintype="text",
                    subtype="csv",
                    filename=csv_path
                )

    # Send email
    try:
        with smtplib.SMTP(SMTP_HOST, SMTP_PORT) as server:
            server.starttls()
            server.login(SENDER_EMAIL, SENDER_PASSWORD)
            server.send_message(msg)
        print("✅ Email sent successfully!")
    except Exception as e:
        print("❌ Failed to send email:", e)

if __name__ == "__main__":
    send_report_email()
